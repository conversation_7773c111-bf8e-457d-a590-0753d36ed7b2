import React from 'react';
import { Heading } from './z-ui/typography/heading';
import { Label } from './z-ui/typography/label';
import { Paragraph } from './z-ui/typography/paragraph';
import { PlayHapticType } from './z-ui/types';

export const ZUIShowcase = () => {
  return (
    <div className="p-8 space-y-12 bg-white min-h-screen">
      {/* Header */}
      <div className="text-center mb-12">
        <Heading size="large" className="mb-4">
          Z-UI Component Library Showcase
        </Heading>
        <Paragraph size="medium" className="text-gray-600">
          Complete overview of all components and their variants
        </Paragraph>
      </div>

      {/* Heading Component */}
      <section className="space-y-6">
        <Heading size="medium" className="border-b pb-2">
          Heading Component
        </Heading>
        <div className="space-y-4">
          <div className="p-4 border rounded-lg">
            <Label size="small" className="text-gray-500 mb-2 block">
              Size: Large (h1)
            </Label>
            <Heading size="large">
              This is a Large Heading
            </Heading>
          </div>
          
          <div className="p-4 border rounded-lg">
            <Label size="small" className="text-gray-500 mb-2 block">
              Size: Medium (h2) - Default
            </Label>
            <Heading size="medium">
              This is a Medium Heading
            </Heading>
          </div>
          
          <div className="p-4 border rounded-lg">
            <Label size="small" className="text-gray-500 mb-2 block">
              Size: Small (h3)
            </Label>
            <Heading size="small">
              This is a Small Heading
            </Heading>
          </div>

          <div className="p-4 border rounded-lg">
            <Label size="small" className="text-gray-500 mb-2 block">
              AsChild Example (renders as span)
            </Label>
            <Heading size="medium" asChild>
              <span>This heading renders as a span element</span>
            </Heading>
          </div>
        </div>
      </section>

      {/* Label Component */}
      <section className="space-y-6">
        <Heading size="medium" className="border-b pb-2">
          Label Component
        </Heading>
        <div className="space-y-4">
          <div className="p-4 border rounded-lg">
            <Label size="small" className="text-gray-500 mb-2 block">
              Size: Large
            </Label>
            <Label size="large">
              This is a Large Label
            </Label>
          </div>
          
          <div className="p-4 border rounded-lg">
            <Label size="small" className="text-gray-500 mb-2 block">
              Size: Medium - Default
            </Label>
            <Label size="medium">
              This is a Medium Label
            </Label>
          </div>
          
          <div className="p-4 border rounded-lg">
            <Label size="small" className="text-gray-500 mb-2 block">
              Size: Small
            </Label>
            <Label size="small">
              This is a Small Label
            </Label>
          </div>
          
          <div className="p-4 border rounded-lg">
            <Label size="small" className="text-gray-500 mb-2 block">
              Size: XSmall
            </Label>
            <Label size="xsmall">
              This is an Extra Small Label
            </Label>
          </div>
        </div>
      </section>

      {/* Paragraph Component */}
      <section className="space-y-6">
        <Heading size="medium" className="border-b pb-2">
          Paragraph Component
        </Heading>
        <div className="space-y-4">
          <div className="p-4 border rounded-lg">
            <Label size="small" className="text-gray-500 mb-2 block">
              Size: Medium - Default
            </Label>
            <Paragraph size="medium">
              This is a medium paragraph. Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
              Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim 
              veniam, quis nostrud exercitation ullamco laboris.
            </Paragraph>
          </div>
          
          <div className="p-4 border rounded-lg">
            <Label size="small" className="text-gray-500 mb-2 block">
              Size: Small
            </Label>
            <Paragraph size="small">
              This is a small paragraph. Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
              Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim 
              veniam, quis nostrud exercitation ullamco laboris.
            </Paragraph>
          </div>
        </div>
      </section>

      {/* Haptic Feedback Types */}
      <section className="space-y-6">
        <Heading size="medium" className="border-b pb-2">
          Haptic Feedback Types
        </Heading>
        <div className="p-4 border rounded-lg">
          <Label size="small" className="text-gray-500 mb-4 block">
            Available Haptic Types (for components that support haptic feedback)
          </Label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.values(PlayHapticType).map((hapticType) => (
              <div key={hapticType} className="p-2 bg-gray-50 rounded text-center">
                <Label size="xsmall">{hapticType}</Label>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Component Properties Summary */}
      <section className="space-y-6">
        <Heading size="medium" className="border-b pb-2">
          Component Properties Summary
        </Heading>
        
        <div className="grid md:grid-cols-3 gap-6">
          {/* Heading Props */}
          <div className="p-4 border rounded-lg">
            <Label size="medium" className="mb-3 block font-semibold">
              Heading Props
            </Label>
            <div className="space-y-2 text-sm">
              <div><strong>size:</strong> "large" | "medium" | "small"</div>
              <div><strong>asChild:</strong> boolean (optional)</div>
              <div><strong>haptic:</strong> PlayHapticType (optional)</div>
              <div><strong>children:</strong> React.ReactNode</div>
              <div><strong>className:</strong> string (optional)</div>
            </div>
          </div>

          {/* Label Props */}
          <div className="p-4 border rounded-lg">
            <Label size="medium" className="mb-3 block font-semibold">
              Label Props
            </Label>
            <div className="space-y-2 text-sm">
              <div><strong>size:</strong> "large" | "medium" | "small" | "xsmall"</div>
              <div><strong>haptic:</strong> PlayHapticType (optional)</div>
              <div><strong>children:</strong> React.ReactNode</div>
              <div><strong>className:</strong> string (optional)</div>
            </div>
          </div>

          {/* Paragraph Props */}
          <div className="p-4 border rounded-lg">
            <Label size="medium" className="mb-3 block font-semibold">
              Paragraph Props
            </Label>
            <div className="space-y-2 text-sm">
              <div><strong>size:</strong> "medium" | "small"</div>
              <div><strong>children:</strong> React.ReactNode</div>
              <div><strong>className:</strong> string (optional)</div>
            </div>
          </div>
        </div>
      </section>

      {/* Usage Examples */}
      <section className="space-y-6">
        <Heading size="medium" className="border-b pb-2">
          Usage Examples
        </Heading>
        
        <div className="space-y-4">
          <div className="p-4 border rounded-lg bg-gray-50">
            <Label size="small" className="text-gray-600 mb-2 block">
              Example: Form Layout
            </Label>
            <div className="space-y-3">
              <Heading size="small">User Information</Heading>
              <div>
                <Label size="medium">Full Name</Label>
                <Paragraph size="small" className="text-gray-600 mt-1">
                  Enter your complete legal name as it appears on your ID
                </Paragraph>
              </div>
            </div>
          </div>

          <div className="p-4 border rounded-lg bg-gray-50">
            <Label size="small" className="text-gray-600 mb-2 block">
              Example: Card Layout
            </Label>
            <div className="space-y-2">
              <Heading size="medium">Payment Summary</Heading>
              <Label size="large" className="text-green-600">$1,234.56</Label>
              <Paragraph size="small" className="text-gray-600">
                Your payment has been processed successfully
              </Paragraph>
              <Label size="xsmall" className="text-gray-400">
                Transaction ID: TXN-123456789
              </Label>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ZUIShowcase;
