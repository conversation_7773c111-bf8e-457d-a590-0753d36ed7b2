import { useEffect, useState } from 'react';
import { Label } from '../z-ui/typography/label';

interface TocItem {
  id: string;
  title: string;
  level: number;
}

export const TableOfContents = () => {
  const [toc, setToc] = useState<TocItem[]>([]);
  const [activeId, setActiveId] = useState<string>('');

  useEffect(() => {
    // Generate table of contents from headings
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    const tocItems: TocItem[] = [];

    headings.forEach((heading, index) => {
      const id = heading.id || `heading-${index}`;
      if (!heading.id) {
        heading.id = id;
      }

      const level = parseInt(heading.tagName.charAt(1));
      const title = heading.textContent || '';

      // Only include h2 and h3 for cleaner TOC
      if (level === 2 || level === 3) {
        tocItems.push({ id, title, level });
      }
    });

    setToc(tocItems);
  }, []);

  useEffect(() => {
    // Track active heading
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveId(entry.target.id);
          }
        });
      },
      {
        rootMargin: '-20% 0% -35% 0%',
      }
    );

    toc.forEach(({ id }) => {
      const element = document.getElementById(id);
      if (element) {
        observer.observe(element);
      }
    });

    return () => observer.disconnect();
  }, [toc]);

  if (toc.length === 0) {
    return null;
  }

  const scrollToHeading = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  };

  return (
    <div className="hidden xl:block">
      <div className="sticky top-20 space-y-2">
        <Label size="small" className="font-semibold text-foreground">
          On This Page
        </Label>
        <nav className="space-y-1">
          {toc.map(({ id, title, level }) => (
            <button
              key={id}
              onClick={() => scrollToHeading(id)}
              className={`block w-full text-left text-sm transition-colors hover:text-foreground ${
                activeId === id
                  ? 'text-foreground font-medium'
                  : 'text-muted-foreground'
              } ${level === 3 ? 'pl-4' : ''}`}
            >
              {title}
            </button>
          ))}
        </nav>
      </div>
    </div>
  );
};
