import { Link, useLocation } from 'react-router-dom';
import { Label } from '../z-ui/typography/label';

interface BreadcrumbItem {
  label: string;
  href?: string;
}

export const Breadcrumb = () => {
  const location = useLocation();
  
  const getBreadcrumbs = (): BreadcrumbItem[] => {
    const path = location.pathname;
    const segments = path.split('/').filter(Boolean);
    
    // Base breadcrumb
    const breadcrumbs: BreadcrumbItem[] = [
      { label: 'Z-UI', href: '/z-ui-screen' }
    ];
    
    // Handle different paths
    if (path === '/z-ui-screen' || path === '/z-ui-screen/') {
      return breadcrumbs;
    }
    
    if (path.includes('/installation')) {
      breadcrumbs.push({ label: 'Installation' });
    } else if (path.includes('/components/')) {
      breadcrumbs.push({ label: 'Components', href: '/z-ui-screen/components' });
      
      if (path.includes('/heading')) {
        breadcrumbs.push({ label: 'Heading' });
      } else if (path.includes('/label')) {
        breadcrumbs.push({ label: 'Label' });
      } else if (path.includes('/paragraph')) {
        breadcrumbs.push({ label: 'Paragraph' });
      }
    } else if (path.includes('/foundation/')) {
      breadcrumbs.push({ label: 'Foundation', href: '/z-ui-screen/foundation' });
      
      if (path.includes('/types')) {
        breadcrumbs.push({ label: 'Types' });
      } else if (path.includes('/haptic')) {
        breadcrumbs.push({ label: 'Haptic Feedback' });
      }
    }
    
    return breadcrumbs;
  };
  
  const breadcrumbs = getBreadcrumbs();
  
  if (breadcrumbs.length <= 1) {
    return null;
  }
  
  return (
    <nav className="flex items-center space-x-1 text-sm text-muted-foreground mb-6">
      {breadcrumbs.map((item, index) => (
        <div key={index} className="flex items-center">
          {index > 0 && (
            <span className="mx-2 text-muted-foreground/50">/</span>
          )}
          {item.href ? (
            <Link 
              to={item.href}
              className="hover:text-foreground transition-colors"
            >
              <Label size="small">{item.label}</Label>
            </Link>
          ) : (
            <Label size="small" className="text-foreground">
              {item.label}
            </Label>
          )}
        </div>
      ))}
    </nav>
  );
};
