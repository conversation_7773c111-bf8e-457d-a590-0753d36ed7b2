import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { Breadcrumb } from './breadcrumb';
import { TableOfContents } from './table-of-contents';
import { MobileNav } from './mobile-nav';

interface NavigationItem {
  title: string;
  href: string;
  description?: string;
}

interface NavigationSection {
  title: string;
  items: NavigationItem[];
}

const navigation: NavigationSection[] = [
  {
    title: "Getting Started",
    items: [
      {
        title: "Overview",
        href: "/z-ui-screen",
        description: "Introduction to Z-UI components"
      },
      {
        title: "Installation",
        href: "/z-ui-screen/installation",
        description: "How to install and setup Z-UI"
      }
    ]
  },
  {
    title: "Typography",
    items: [
      {
        title: "Heading",
        href: "/z-ui-screen/components/heading",
        description: "Display headings with different sizes"
      },
      {
        title: "Label",
        href: "/z-ui-screen/components/label",
        description: "Form labels and text labeling"
      },
      {
        title: "Paragraph",
        href: "/z-ui-screen/components/paragraph",
        description: "Body text and content paragraphs"
      }
    ]
  },
  {
    title: "Foundation",
    items: [
      {
        title: "Types",
        href: "/z-ui-screen/foundation/types",
        description: "Shared types and interfaces"
      },
      {
        title: "Haptic Feedback",
        href: "/z-ui-screen/foundation/haptic",
        description: "Haptic feedback system"
      }
    ]
  }
];

interface ZUILayoutProps {
  children: React.ReactNode;
}

export const ZUILayout = ({ children }: ZUILayoutProps) => {
  const location = useLocation();

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex items-center h-14">
          <Link to="/z-ui-screen" className="flex items-center mr-6 space-x-2">
            <span className="text-xl font-bold">Z-UI</span>
          </Link>
          <nav className="items-center hidden space-x-6 text-sm font-medium md:flex">
            <Link
              to="/z-ui-screen"
              className={cn(
                "transition-colors hover:text-foreground/80",
                location.pathname === "/z-ui-screen" ? "text-foreground" : "text-foreground/60"
              )}
            >
              Documentation
            </Link>
            <Link
              to="/z-ui-screen/components"
              className={cn(
                "transition-colors hover:text-foreground/80",
                location.pathname.startsWith("/z-ui-screen/components") ? "text-foreground" : "text-foreground/60"
              )}
            >
              Components
            </Link>
          </nav>
          <div className="ml-auto">
            <MobileNav />
          </div>
        </div>
      </header>

      <div className="container flex-1 items-start md:grid md:grid-cols-[220px_minmax(0,1fr)] md:gap-6 lg:grid-cols-[240px_minmax(0,1fr)] lg:gap-10">
        {/* Sidebar */}
        <aside className="fixed top-14 z-30 -ml-2 hidden h-[calc(100vh-3.5rem)] w-full shrink-0 md:sticky md:block">
          <div className="h-full py-6 pr-6 lg:py-8">
            <div className="w-full">
              {navigation.map((section) => (
                <div key={section.title} className="pb-4">
                  <h4 className="px-2 py-1 mb-1 text-sm font-semibold rounded-md">
                    {section.title}
                  </h4>
                  <div className="grid grid-flow-row text-sm auto-rows-max">
                    {section.items.map((item) => (
                      <Link
                        key={item.href}
                        to={item.href}
                        className={cn(
                          "group flex w-full items-center rounded-md border border-transparent px-2 py-1 hover:underline",
                          location.pathname === item.href
                            ? "font-medium text-foreground"
                            : "text-muted-foreground"
                        )}
                      >
                        {item.title}
                      </Link>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </aside>

        {/* Main Content */}
        <main className="relative py-6 lg:gap-10 lg:py-8 xl:grid xl:grid-cols-[1fr_300px]">
          <div className="w-full min-w-0 mx-auto">
            <Breadcrumb />
            {children}
          </div>

          {/* Table of Contents */}
          <div className="hidden text-sm xl:block">
            <TableOfContents />
          </div>
        </main>
      </div>
    </div>
  );
};
