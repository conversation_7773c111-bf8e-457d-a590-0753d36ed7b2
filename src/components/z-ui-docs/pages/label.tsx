import { Heading } from '../../z-ui/typography/heading';
import { Label } from '../../z-ui/typography/label';
import { Paragraph } from '../../z-ui/typography/paragraph';
import { PlayHapticType } from '../../z-ui/types';

const LabelPage = () => {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <Heading size="large">Label</Heading>
        <Paragraph size="medium" className="text-muted-foreground">
          A versatile label component for form elements, metadata, and general text labeling with multiple size variants.
        </Paragraph>
      </div>

      {/* Examples */}
      <div className="space-y-6">
        <Heading size="medium">Examples</Heading>
        
        {/* Size Variants */}
        <div className="space-y-4">
          <Label size="medium" className="font-semibold">Size Variants</Label>
          <div className="rounded-lg border p-6 space-y-4">
            <div className="space-y-2">
              <Label size="small" className="text-muted-foreground">Large</Label>
              <Label size="large">Large Label Text</Label>
            </div>
            
            <div className="space-y-2">
              <Label size="small" className="text-muted-foreground">Medium - Default</Label>
              <Label size="medium">Medium Label Text</Label>
            </div>
            
            <div className="space-y-2">
              <Label size="small" className="text-muted-foreground">Small</Label>
              <Label size="small">Small Label Text</Label>
            </div>
            
            <div className="space-y-2">
              <Label size="small" className="text-muted-foreground">Extra Small</Label>
              <Label size="xsmall">Extra Small Label Text</Label>
            </div>
          </div>
        </div>

        {/* Form Usage */}
        <div className="space-y-4">
          <Label size="medium" className="font-semibold">Form Usage</Label>
          <div className="rounded-lg border p-6 space-y-4">
            <div className="space-y-2">
              <Label size="medium">Email Address</Label>
              <div className="h-10 w-full rounded border bg-muted/50 px-3 flex items-center text-sm text-muted-foreground">
                <EMAIL>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label size="medium">Full Name</Label>
              <Label size="small" className="text-muted-foreground">Enter your complete legal name</Label>
              <div className="h-10 w-full rounded border bg-muted/50 px-3 flex items-center text-sm text-muted-foreground">
                John Doe
              </div>
            </div>
          </div>
        </div>

        {/* Metadata Usage */}
        <div className="space-y-4">
          <Label size="medium" className="font-semibold">Metadata & Status</Label>
          <div className="rounded-lg border p-6 space-y-4">
            <div className="flex items-center justify-between">
              <Label size="medium">Transaction Amount</Label>
              <Label size="large" className="text-green-600">$1,234.56</Label>
            </div>
            
            <div className="flex items-center justify-between">
              <Label size="small">Status</Label>
              <Label size="small" className="text-blue-600">Processing</Label>
            </div>
            
            <div className="flex items-center justify-between">
              <Label size="xsmall" className="text-muted-foreground">Transaction ID</Label>
              <Label size="xsmall" className="font-mono">TXN-123456789</Label>
            </div>
          </div>
        </div>

        {/* Code Examples */}
        <div className="space-y-4">
          <Label size="medium" className="font-semibold">Code Examples</Label>
          <div className="space-y-4">
            <div>
              <Label size="small" className="text-muted-foreground mb-2 block">Basic Usage</Label>
              <div className="rounded bg-muted p-4 text-sm font-mono">
                {`<Label size="large">Important Label</Label>
<Label size="medium">Standard Label</Label>
<Label size="small">Secondary Label</Label>
<Label size="xsmall">Metadata Label</Label>`}
              </div>
            </div>
            
            <div>
              <Label size="small" className="text-muted-foreground mb-2 block">Form Labels</Label>
              <div className="rounded bg-muted p-4 text-sm font-mono">
                {`<Label size="medium">Email Address</Label>
<input type="email" />

<Label size="small" className="text-muted-foreground">
  Helper text for the field
</Label>`}
              </div>
            </div>
            
            <div>
              <Label size="small" className="text-muted-foreground mb-2 block">With Custom Styling</Label>
              <div className="rounded bg-muted p-4 text-sm font-mono">
                {`<Label size="medium" className="text-red-600 font-semibold">
  Error Label
</Label>`}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* API Reference */}
      <div className="space-y-6">
        <Heading size="medium">API Reference</Heading>
        
        <div className="space-y-4">
          <Label size="medium" className="font-semibold">Props</Label>
          <div className="rounded-lg border">
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-4 font-medium">Prop</th>
                    <th className="text-left p-4 font-medium">Type</th>
                    <th className="text-left p-4 font-medium">Default</th>
                    <th className="text-left p-4 font-medium">Description</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b">
                    <td className="p-4 font-mono">size</td>
                    <td className="p-4 font-mono">"large" | "medium" | "small" | "xsmall"</td>
                    <td className="p-4 font-mono">"medium"</td>
                    <td className="p-4">Controls the label text size</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-4 font-mono">haptic</td>
                    <td className="p-4 font-mono">PlayHapticType</td>
                    <td className="p-4 font-mono">undefined</td>
                    <td className="p-4">Haptic feedback type for interactions</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-4 font-mono">children</td>
                    <td className="p-4 font-mono">React.ReactNode</td>
                    <td className="p-4 font-mono">-</td>
                    <td className="p-4">Content to be rendered inside the label</td>
                  </tr>
                  <tr>
                    <td className="p-4 font-mono">className</td>
                    <td className="p-4 font-mono">string</td>
                    <td className="p-4 font-mono">undefined</td>
                    <td className="p-4">Additional CSS classes</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <Label size="medium" className="font-semibold">Size Guide</Label>
          <div className="rounded-lg border p-4">
            <div className="space-y-2 text-sm">
              <div><span className="font-mono">large</span> - Primary labels, important values</div>
              <div><span className="font-mono">medium</span> - Standard form labels, general text</div>
              <div><span className="font-mono">small</span> - Secondary labels, helper text</div>
              <div><span className="font-mono">xsmall</span> - Metadata, timestamps, IDs</div>
            </div>
          </div>
        </div>
      </div>

      {/* Usage Guidelines */}
      <div className="space-y-6">
        <Heading size="medium">Usage Guidelines</Heading>
        
        <div className="space-y-4">
          <div className="rounded-lg border p-6">
            <Label size="medium" className="font-semibold text-green-600 mb-2 block">✅ Do</Label>
            <ul className="space-y-1 text-sm">
              <li>• Use appropriate size based on content hierarchy</li>
              <li>• Provide clear, descriptive label text</li>
              <li>• Use xsmall for metadata and secondary information</li>
              <li>• Apply consistent styling for similar content types</li>
            </ul>
          </div>
          
          <div className="rounded-lg border p-6">
            <Label size="medium" className="font-semibold text-red-600 mb-2 block">❌ Don't</Label>
            <ul className="space-y-1 text-sm">
              <li>• Use labels for long paragraphs of text</li>
              <li>• Mix label sizes inconsistently</li>
              <li>• Use large labels for unimportant information</li>
              <li>• Forget to associate labels with form controls</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Common Patterns */}
      <div className="space-y-6">
        <Heading size="medium">Common Patterns</Heading>
        
        <div className="space-y-4">
          <div className="rounded-lg border p-6">
            <Label size="medium" className="font-semibold mb-4 block">Card Information Display</Label>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <Label size="medium">Total Amount</Label>
                <Label size="large" className="text-green-600">$1,234.56</Label>
              </div>
              <div className="flex justify-between items-center">
                <Label size="small">Processing Fee</Label>
                <Label size="small">$12.34</Label>
              </div>
              <div className="flex justify-between items-center">
                <Label size="xsmall" className="text-muted-foreground">Reference</Label>
                <Label size="xsmall" className="font-mono">REF-789</Label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LabelPage;
