import { Heading } from '../../z-ui/typography/heading';
import { Label } from '../../z-ui/typography/label';
import { Paragraph } from '../../z-ui/typography/paragraph';

const ParagraphPage = () => {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <Heading size="large">Paragraph</Heading>
        <Paragraph size="medium" className="text-muted-foreground">
          A paragraph component for body text content with consistent typography and spacing.
        </Paragraph>
      </div>

      {/* Examples */}
      <div className="space-y-6">
        <Heading size="medium">Examples</Heading>
        
        {/* Size Variants */}
        <div className="space-y-4">
          <Label size="medium" className="font-semibold">Size Variants</Label>
          <div className="rounded-lg border p-6 space-y-6">
            <div className="space-y-2">
              <Label size="small" className="text-muted-foreground">Medium - Default</Label>
              <Paragraph size="medium">
                This is a medium paragraph with standard body text. It's perfect for main content, 
                descriptions, and general information that users need to read. The text size is 
                optimized for readability and comfortable reading experience.
              </Paragraph>
            </div>
            
            <div className="space-y-2">
              <Label size="small" className="text-muted-foreground">Small</Label>
              <Paragraph size="small">
                This is a small paragraph used for secondary content, fine print, disclaimers, 
                or additional details. It's smaller than the standard text but still maintains 
                good readability for important supplementary information.
              </Paragraph>
            </div>
          </div>
        </div>

        {/* Content Examples */}
        <div className="space-y-4">
          <Label size="medium" className="font-semibold">Content Examples</Label>
          <div className="rounded-lg border p-6 space-y-6">
            <div className="space-y-3">
              <Heading size="small">Product Description</Heading>
              <Paragraph size="medium">
                Our Buy Now Pay Later service allows you to split your purchase into manageable 
                installments. Shop today and pay over time with flexible payment options that 
                fit your budget and lifestyle.
              </Paragraph>
              <Paragraph size="small" className="text-muted-foreground">
                Subject to credit approval. Terms and conditions apply.
              </Paragraph>
            </div>
            
            <div className="space-y-3">
              <Heading size="small">Help Text</Heading>
              <Paragraph size="medium">
                To get started with your payment plan, simply select the items you want to 
                purchase and choose your preferred payment schedule at checkout.
              </Paragraph>
              <Paragraph size="small">
                You'll receive email notifications before each payment is due, and you can 
                manage your payment schedule through your account dashboard.
              </Paragraph>
            </div>
          </div>
        </div>

        {/* Layout Examples */}
        <div className="space-y-4">
          <Label size="medium" className="font-semibold">Layout Examples</Label>
          <div className="rounded-lg border p-6 space-y-6">
            <div className="max-w-2xl">
              <Heading size="medium" className="mb-4">Terms of Service</Heading>
              <div className="space-y-4">
                <Paragraph size="medium">
                  By using our Buy Now Pay Later service, you agree to these terms and conditions. 
                  Please read them carefully before proceeding with your purchase.
                </Paragraph>
                <Paragraph size="medium">
                  Payment schedules are automatically set up based on your selected plan. 
                  You can modify or cancel your plan within the first 24 hours of purchase.
                </Paragraph>
                <Paragraph size="small" className="text-muted-foreground">
                  Last updated: January 2024. These terms may be updated periodically.
                </Paragraph>
              </div>
            </div>
          </div>
        </div>

        {/* Code Examples */}
        <div className="space-y-4">
          <Label size="medium" className="font-semibold">Code Examples</Label>
          <div className="space-y-4">
            <div>
              <Label size="small" className="text-muted-foreground mb-2 block">Basic Usage</Label>
              <div className="rounded bg-muted p-4 text-sm font-mono">
                {`<Paragraph size="medium">
  This is standard body text for main content.
</Paragraph>

<Paragraph size="small">
  This is smaller text for secondary content.
</Paragraph>`}
              </div>
            </div>
            
            <div>
              <Label size="small" className="text-muted-foreground mb-2 block">With Custom Styling</Label>
              <div className="rounded bg-muted p-4 text-sm font-mono">
                {`<Paragraph size="medium" className="text-muted-foreground">
  Muted paragraph text
</Paragraph>

<Paragraph size="small" className="text-red-600">
  Error message text
</Paragraph>`}
              </div>
            </div>
            
            <div>
              <Label size="small" className="text-muted-foreground mb-2 block">Content Structure</Label>
              <div className="rounded bg-muted p-4 text-sm font-mono">
                {`<div className="space-y-4">
  <Heading size="medium">Section Title</Heading>
  <Paragraph size="medium">
    Main content paragraph with important information.
  </Paragraph>
  <Paragraph size="small" className="text-muted-foreground">
    Additional details or disclaimers.
  </Paragraph>
</div>`}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* API Reference */}
      <div className="space-y-6">
        <Heading size="medium">API Reference</Heading>
        
        <div className="space-y-4">
          <Label size="medium" className="font-semibold">Props</Label>
          <div className="rounded-lg border">
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-4 font-medium">Prop</th>
                    <th className="text-left p-4 font-medium">Type</th>
                    <th className="text-left p-4 font-medium">Default</th>
                    <th className="text-left p-4 font-medium">Description</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b">
                    <td className="p-4 font-mono">size</td>
                    <td className="p-4 font-mono">"medium" | "small"</td>
                    <td className="p-4 font-mono">"medium"</td>
                    <td className="p-4">Controls the paragraph text size</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-4 font-mono">children</td>
                    <td className="p-4 font-mono">React.ReactNode</td>
                    <td className="p-4 font-mono">-</td>
                    <td className="p-4">Content to be rendered inside the paragraph</td>
                  </tr>
                  <tr>
                    <td className="p-4 font-mono">className</td>
                    <td className="p-4 font-mono">string</td>
                    <td className="p-4 font-mono">undefined</td>
                    <td className="p-4">Additional CSS classes</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <Label size="medium" className="font-semibold">Size Guide</Label>
          <div className="rounded-lg border p-4">
            <div className="space-y-2 text-sm">
              <div><span className="font-mono">medium</span> - Standard body text, main content</div>
              <div><span className="font-mono">small</span> - Secondary content, fine print, disclaimers</div>
            </div>
          </div>
        </div>
      </div>

      {/* Usage Guidelines */}
      <div className="space-y-6">
        <Heading size="medium">Usage Guidelines</Heading>
        
        <div className="space-y-4">
          <div className="rounded-lg border p-6">
            <Label size="medium" className="font-semibold text-green-600 mb-2 block">✅ Do</Label>
            <ul className="space-y-1 text-sm">
              <li>• Use medium size for main body content</li>
              <li>• Use small size for disclaimers and fine print</li>
              <li>• Maintain consistent line spacing between paragraphs</li>
              <li>• Keep paragraph length reasonable for readability</li>
            </ul>
          </div>
          
          <div className="rounded-lg border p-6">
            <Label size="medium" className="font-semibold text-red-600 mb-2 block">❌ Don't</Label>
            <ul className="space-y-1 text-sm">
              <li>• Use paragraphs for single words or short phrases</li>
              <li>• Create overly long paragraphs that are hard to read</li>
              <li>• Mix paragraph sizes inconsistently</li>
              <li>• Use paragraphs for headings or labels</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Common Patterns */}
      <div className="space-y-6">
        <Heading size="medium">Common Patterns</Heading>
        
        <div className="space-y-4">
          <div className="rounded-lg border p-6">
            <Label size="medium" className="font-semibold mb-4 block">Article Layout</Label>
            <div className="max-w-2xl space-y-4">
              <Heading size="medium">How Buy Now Pay Later Works</Heading>
              <Paragraph size="medium">
                Buy Now Pay Later allows you to purchase items immediately and pay for them 
                over time in smaller, more manageable installments.
              </Paragraph>
              <Paragraph size="medium">
                When you choose this payment option at checkout, your total purchase amount 
                is divided into equal payments spread over several weeks or months.
              </Paragraph>
              <Paragraph size="small" className="text-muted-foreground">
                Interest rates and fees may apply depending on your payment plan.
              </Paragraph>
            </div>
          </div>
          
          <div className="rounded-lg border p-6">
            <Label size="medium" className="font-semibold mb-4 block">Feature Description</Label>
            <div className="space-y-3">
              <Heading size="small">Flexible Payment Options</Heading>
              <Paragraph size="medium">
                Choose from multiple payment schedules that work with your budget.
              </Paragraph>
              <Paragraph size="small">
                Available options: 4 payments over 6 weeks, 6 payments over 3 months, 
                or 12 payments over 6 months.
              </Paragraph>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ParagraphPage;
