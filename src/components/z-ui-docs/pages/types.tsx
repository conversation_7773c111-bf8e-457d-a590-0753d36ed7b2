import { Heading } from '../../z-ui/typography/heading';
import { Label } from '../../z-ui/typography/label';
import { Paragraph } from '../../z-ui/typography/paragraph';
import { PlayHapticType } from '../../z-ui/types';

const TypesPage = () => {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <Heading size="large">Types</Heading>
        <Paragraph size="medium" className="text-muted-foreground">
          Shared TypeScript types and interfaces used throughout the Z-UI component library.
        </Paragraph>
      </div>

      {/* HapticFeedback Interface */}
      <div className="space-y-6">
        <Heading size="medium">HapticFeedback Interface</Heading>
        
        <div className="space-y-4">
          <Paragraph size="medium">
            The HapticFeedback interface provides haptic feedback support for interactive components.
          </Paragraph>
          
          <div className="rounded-lg border p-4">
            <Label size="small" className="text-muted-foreground mb-2 block">Interface Definition</Label>
            <div className="rounded bg-muted p-4 text-sm font-mono">
              {`interface HapticFeedback {
  haptic?: PlayHapticType
}`}
            </div>
          </div>

          <div className="space-y-4">
            <Label size="medium" className="font-semibold">Properties</Label>
            <div className="rounded-lg border">
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-4 font-medium">Property</th>
                      <th className="text-left p-4 font-medium">Type</th>
                      <th className="text-left p-4 font-medium">Required</th>
                      <th className="text-left p-4 font-medium">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="p-4 font-mono">haptic</td>
                      <td className="p-4 font-mono">PlayHapticType</td>
                      <td className="p-4">No</td>
                      <td className="p-4">Specifies the type of haptic feedback to trigger</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* PlayHapticType Enum */}
      <div className="space-y-6">
        <Heading size="medium">PlayHapticType Enum</Heading>
        
        <div className="space-y-4">
          <Paragraph size="medium">
            The PlayHapticType enum defines all available haptic feedback types for enhanced user interaction.
          </Paragraph>
          
          <div className="rounded-lg border p-4">
            <Label size="small" className="text-muted-foreground mb-2 block">Enum Definition</Label>
            <div className="rounded bg-muted p-4 text-sm font-mono">
              {`enum PlayHapticType {
  HeavyTap = "HeavyTap",
  LightTap = "LightTap",
  Selection = "Selection",
  MediumTap = "MediumTap",
  Custom = "Custom",
  Success = "Success",
  Error = "Error",
  Soft = "Soft",
}`}
            </div>
          </div>

          <div className="space-y-4">
            <Label size="medium" className="font-semibold">Haptic Types</Label>
            <div className="rounded-lg border">
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-4 font-medium">Type</th>
                      <th className="text-left p-4 font-medium">Intensity</th>
                      <th className="text-left p-4 font-medium">Use Case</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b">
                      <td className="p-4 font-mono">HeavyTap</td>
                      <td className="p-4">Strong</td>
                      <td className="p-4">Important actions, confirmations</td>
                    </tr>
                    <tr className="border-b">
                      <td className="p-4 font-mono">MediumTap</td>
                      <td className="p-4">Medium</td>
                      <td className="p-4">Standard button presses, interactions</td>
                    </tr>
                    <tr className="border-b">
                      <td className="p-4 font-mono">LightTap</td>
                      <td className="p-4">Light</td>
                      <td className="p-4">Subtle interactions, hover effects</td>
                    </tr>
                    <tr className="border-b">
                      <td className="p-4 font-mono">Selection</td>
                      <td className="p-4">Light</td>
                      <td className="p-4">List selections, picker changes</td>
                    </tr>
                    <tr className="border-b">
                      <td className="p-4 font-mono">Success</td>
                      <td className="p-4">Medium</td>
                      <td className="p-4">Successful operations, completions</td>
                    </tr>
                    <tr className="border-b">
                      <td className="p-4 font-mono">Error</td>
                      <td className="p-4">Strong</td>
                      <td className="p-4">Error states, failed operations</td>
                    </tr>
                    <tr className="border-b">
                      <td className="p-4 font-mono">Soft</td>
                      <td className="p-4">Very Light</td>
                      <td className="p-4">Gentle feedback, background actions</td>
                    </tr>
                    <tr>
                      <td className="p-4 font-mono">Custom</td>
                      <td className="p-4">Variable</td>
                      <td className="p-4">Custom haptic patterns</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Usage Examples */}
      <div className="space-y-6">
        <Heading size="medium">Usage Examples</Heading>
        
        <div className="space-y-4">
          <Label size="medium" className="font-semibold">Component Implementation</Label>
          <div className="rounded bg-muted p-4 text-sm font-mono">
            {`import { PlayHapticType } from '@/components/z-ui/types';

// Using with Heading component
<Heading size="large" haptic={PlayHapticType.MediumTap}>
  Interactive Heading
</Heading>

// Using with Label component
<Label size="medium" haptic={PlayHapticType.Selection}>
  Selectable Label
</Label>`}
          </div>
        </div>

        <div className="space-y-4">
          <Label size="medium" className="font-semibold">Custom Component with Haptic Support</Label>
          <div className="rounded bg-muted p-4 text-sm font-mono">
            {`interface MyComponentProps extends HapticFeedback {
  title: string;
  onClick: () => void;
}

const MyComponent = ({ title, haptic, onClick }: MyComponentProps) => {
  const handleClick = () => {
    // Trigger haptic feedback if specified
    if (haptic) {
      // Implementation depends on your haptic system
      triggerHaptic(haptic);
    }
    onClick();
  };

  return (
    <button onClick={handleClick}>
      {title}
    </button>
  );
};`}
          </div>
        </div>
      </div>

      {/* Best Practices */}
      <div className="space-y-6">
        <Heading size="medium">Best Practices</Heading>
        
        <div className="space-y-4">
          <div className="rounded-lg border p-6">
            <Label size="medium" className="font-semibold text-green-600 mb-2 block">✅ Do</Label>
            <ul className="space-y-1 text-sm">
              <li>• Use appropriate haptic intensity for the action importance</li>
              <li>• Be consistent with haptic feedback across similar interactions</li>
              <li>• Use Success haptic for positive confirmations</li>
              <li>• Use Error haptic for critical failures or warnings</li>
              <li>• Test haptic feedback on actual devices</li>
            </ul>
          </div>
          
          <div className="rounded-lg border p-6">
            <Label size="medium" className="font-semibold text-red-600 mb-2 block">❌ Don't</Label>
            <ul className="space-y-1 text-sm">
              <li>• Overuse haptic feedback - it can become annoying</li>
              <li>• Use strong haptics for minor interactions</li>
              <li>• Ignore user preferences for haptic feedback</li>
              <li>• Use haptics without considering battery impact</li>
              <li>• Assume all devices support haptic feedback</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Platform Considerations */}
      <div className="space-y-6">
        <Heading size="medium">Platform Considerations</Heading>
        
        <div className="space-y-4">
          <div className="rounded-lg border p-6">
            <Label size="medium" className="font-semibold mb-3 block">Mobile Devices</Label>
            <Paragraph size="small">
              Haptic feedback is most effective on mobile devices with built-in haptic engines. 
              iOS and Android devices support different levels of haptic feedback intensity and patterns.
            </Paragraph>
          </div>
          
          <div className="rounded-lg border p-6">
            <Label size="medium" className="font-semibold mb-3 block">Web Browsers</Label>
            <Paragraph size="small">
              Web browsers have limited haptic support. The Vibration API is available in some browsers 
              but may not provide the same quality of feedback as native mobile apps.
            </Paragraph>
          </div>
          
          <div className="rounded-lg border p-6">
            <Label size="medium" className="font-semibold mb-3 block">Accessibility</Label>
            <Paragraph size="small">
              Always provide alternative feedback methods (visual, audio) alongside haptic feedback. 
              Some users may have haptic feedback disabled or may not be able to perceive it.
            </Paragraph>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TypesPage;
