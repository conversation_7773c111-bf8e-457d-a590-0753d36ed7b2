import { Link } from 'react-router-dom';
import { Heading } from '../../z-ui/typography/heading';
import { Label } from '../../z-ui/typography/label';
import { Paragraph } from '../../z-ui/typography/paragraph';

const OverviewPage = () => {
  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="space-y-4">
        <Heading size="large">Z-UI Component Library</Heading>
        <Paragraph size="medium" className="text-muted-foreground max-w-2xl">
          A comprehensive typography component library built for the Buy Now Pay Later app. 
          Designed with consistency, accessibility, and developer experience in mind.
        </Paragraph>
      </div>

      {/* Quick Start */}
      <div className="space-y-4">
        <Heading size="medium">Quick Start</Heading>
        <div className="grid gap-4 md:grid-cols-2">
          <div className="rounded-lg border p-6">
            <Heading size="small" className="mb-2">Typography Components</Heading>
            <Paragraph size="small" className="text-muted-foreground mb-4">
              Ready-to-use heading, label, and paragraph components with multiple size variants.
            </Paragraph>
            <Link 
              to="/z-ui-screen/components/heading"
              className="inline-flex items-center text-sm font-medium text-primary hover:underline"
            >
              View Components →
            </Link>
          </div>
          
          <div className="rounded-lg border p-6">
            <Heading size="small" className="mb-2">Haptic Feedback</Heading>
            <Paragraph size="small" className="text-muted-foreground mb-4">
              Built-in haptic feedback support for enhanced user interaction experience.
            </Paragraph>
            <Link 
              to="/z-ui-screen/foundation/haptic"
              className="inline-flex items-center text-sm font-medium text-primary hover:underline"
            >
              Learn More →
            </Link>
          </div>
        </div>
      </div>

      {/* Component Preview */}
      <div className="space-y-4">
        <Heading size="medium">Component Preview</Heading>
        <div className="rounded-lg border p-6 space-y-6">
          <div className="space-y-2">
            <Label size="small" className="text-muted-foreground">Heading Component</Label>
            <div className="space-y-2">
              <Heading size="large">Large Heading</Heading>
              <Heading size="medium">Medium Heading</Heading>
              <Heading size="small">Small Heading</Heading>
            </div>
          </div>

          <div className="space-y-2">
            <Label size="small" className="text-muted-foreground">Label Component</Label>
            <div className="space-y-1">
              <Label size="large">Large Label</Label>
              <Label size="medium">Medium Label</Label>
              <Label size="small">Small Label</Label>
              <Label size="xsmall">Extra Small Label</Label>
            </div>
          </div>

          <div className="space-y-2">
            <Label size="small" className="text-muted-foreground">Paragraph Component</Label>
            <div className="space-y-2">
              <Paragraph size="medium">
                This is a medium paragraph with sample text to demonstrate the typography styling.
              </Paragraph>
              <Paragraph size="small">
                This is a small paragraph with sample text to demonstrate the typography styling.
              </Paragraph>
            </div>
          </div>
        </div>
      </div>

      {/* Features */}
      <div className="space-y-4">
        <Heading size="medium">Features</Heading>
        <div className="grid gap-4 md:grid-cols-3">
          <div className="space-y-2">
            <Label size="medium" className="font-semibold">🎨 Consistent Design</Label>
            <Paragraph size="small" className="text-muted-foreground">
              All components follow a unified design system with consistent spacing and typography.
            </Paragraph>
          </div>
          
          <div className="space-y-2">
            <Label size="medium" className="font-semibold">📱 Haptic Support</Label>
            <Paragraph size="small" className="text-muted-foreground">
              Built-in haptic feedback integration for enhanced mobile user experience.
            </Paragraph>
          </div>
          
          <div className="space-y-2">
            <Label size="medium" className="font-semibold">🔧 Developer Friendly</Label>
            <Paragraph size="small" className="text-muted-foreground">
              TypeScript support, clear APIs, and comprehensive documentation.
            </Paragraph>
          </div>
        </div>
      </div>

      {/* Getting Started */}
      <div className="space-y-4">
        <Heading size="medium">Getting Started</Heading>
        <div className="rounded-lg border p-6">
          <div className="space-y-4">
            <div>
              <Label size="medium" className="font-semibold">1. Import Components</Label>
              <div className="mt-2 rounded bg-muted p-3 text-sm font-mono">
                {`import { Heading, Label, Paragraph } from '@/components/z-ui/typography';`}
              </div>
            </div>
            
            <div>
              <Label size="medium" className="font-semibold">2. Use in Your App</Label>
              <div className="mt-2 rounded bg-muted p-3 text-sm font-mono">
                {`<Heading size="large">Welcome</Heading>
<Paragraph size="medium">Get started with Z-UI components.</Paragraph>`}
              </div>
            </div>
            
            <div>
              <Label size="medium" className="font-semibold">3. Explore Components</Label>
              <Paragraph size="small" className="text-muted-foreground mt-2">
                Browse the component documentation to learn about all available props and variants.
              </Paragraph>
            </div>
          </div>
        </div>
      </div>

      {/* Next Steps */}
      <div className="space-y-4">
        <Heading size="medium">Next Steps</Heading>
        <div className="grid gap-4 md:grid-cols-2">
          <Link 
            to="/z-ui-screen/components/heading"
            className="rounded-lg border p-4 hover:bg-muted/50 transition-colors"
          >
            <Label size="medium" className="font-semibold">Explore Components</Label>
            <Paragraph size="small" className="text-muted-foreground mt-1">
              Learn about each component and see live examples
            </Paragraph>
          </Link>
          
          <Link 
            to="/z-ui-screen/foundation/types"
            className="rounded-lg border p-4 hover:bg-muted/50 transition-colors"
          >
            <Label size="medium" className="font-semibold">Foundation</Label>
            <Paragraph size="small" className="text-muted-foreground mt-1">
              Understand the underlying types and design principles
            </Paragraph>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default OverviewPage;
