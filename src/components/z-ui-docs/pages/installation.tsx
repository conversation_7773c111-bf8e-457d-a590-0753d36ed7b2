import { Heading } from '../../z-ui/typography/heading';
import { Label } from '../../z-ui/typography/label';
import { Paragraph } from '../../z-ui/typography/paragraph';

const InstallationPage = () => {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <Heading size="large">Installation</Heading>
        <Paragraph size="medium" className="text-muted-foreground">
          Learn how to install and set up Z-UI components in your Buy Now Pay Later application.
        </Paragraph>
      </div>

      {/* Prerequisites */}
      <div className="space-y-6">
        <Heading size="medium">Prerequisites</Heading>
        
        <div className="space-y-4">
          <Paragraph size="medium">
            Z-UI components are built for React applications and require the following dependencies:
          </Paragraph>
          
          <div className="rounded-lg border p-4">
            <Label size="small" className="text-muted-foreground mb-2 block">Required Dependencies</Label>
            <div className="rounded bg-muted p-4 text-sm font-mono space-y-1">
              <div>React ^18.0.0</div>
              <div>@radix-ui/react-slot</div>
              <div>class-variance-authority</div>
              <div>Tailwind CSS</div>
            </div>
          </div>
        </div>
      </div>

      {/* Installation Steps */}
      <div className="space-y-6">
        <Heading size="medium">Installation Steps</Heading>
        
        <div className="space-y-6">
          <div className="space-y-4">
            <Label size="medium" className="font-semibold">1. Install Dependencies</Label>
            <Paragraph size="medium">
              Install the required dependencies using your preferred package manager:
            </Paragraph>
            
            <div className="space-y-3">
              <div>
                <Label size="small" className="text-muted-foreground mb-2 block">npm</Label>
                <div className="rounded bg-muted p-4 text-sm font-mono">
                  npm install @radix-ui/react-slot class-variance-authority
                </div>
              </div>
              
              <div>
                <Label size="small" className="text-muted-foreground mb-2 block">yarn</Label>
                <div className="rounded bg-muted p-4 text-sm font-mono">
                  yarn add @radix-ui/react-slot class-variance-authority
                </div>
              </div>
              
              <div>
                <Label size="small" className="text-muted-foreground mb-2 block">pnpm</Label>
                <div className="rounded bg-muted p-4 text-sm font-mono">
                  pnpm add @radix-ui/react-slot class-variance-authority
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <Label size="medium" className="font-semibold">2. Set Up Tailwind CSS</Label>
            <Paragraph size="medium">
              Z-UI components use Tailwind CSS for styling. Make sure Tailwind is configured in your project:
            </Paragraph>
            
            <div className="rounded bg-muted p-4 text-sm font-mono">
              {`// tailwind.config.js
module.exports = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      // Add custom typography classes
      fontSize: {
        'heading-large': ['2rem', { lineHeight: '2.5rem' }],
        'heading-medium': ['1.5rem', { lineHeight: '2rem' }],
        'heading-small': ['1.25rem', { lineHeight: '1.75rem' }],
        'label-large': ['1rem', { lineHeight: '1.5rem' }],
        'label-medium': ['0.875rem', { lineHeight: '1.25rem' }],
        'label-small': ['0.75rem', { lineHeight: '1rem' }],
        'label-xsmall': ['0.625rem', { lineHeight: '0.875rem' }],
        'paragraph-medium': ['1rem', { lineHeight: '1.5rem' }],
        'paragraph-small': ['0.875rem', { lineHeight: '1.25rem' }],
      },
      colors: {
        'dark-500': '#6b7280', // Customize as needed
      }
    },
  },
  plugins: [],
}`}
            </div>
          </div>

          <div className="space-y-4">
            <Label size="medium" className="font-semibold">3. Set Up Utility Functions</Label>
            <Paragraph size="medium">
              Create the utility function for class name merging:
            </Paragraph>
            
            <div className="rounded bg-muted p-4 text-sm font-mono">
              {`// src/lib/utils.ts
import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}`}
            </div>
          </div>

          <div className="space-y-4">
            <Label size="medium" className="font-semibold">4. Copy Component Files</Label>
            <Paragraph size="medium">
              Copy the Z-UI component files to your project structure:
            </Paragraph>
            
            <div className="rounded bg-muted p-4 text-sm font-mono">
              {`src/
├── components/
│   └── z-ui/
│       ├── types.ts
│       └── typography/
│           ├── heading.tsx
│           ├── label.tsx
│           └── paragraph.tsx
└── lib/
    └── utils.ts`}
            </div>
          </div>
        </div>
      </div>

      {/* Usage */}
      <div className="space-y-6">
        <Heading size="medium">Usage</Heading>
        
        <div className="space-y-4">
          <Paragraph size="medium">
            Once installed, you can import and use Z-UI components in your React application:
          </Paragraph>
          
          <div className="rounded bg-muted p-4 text-sm font-mono">
            {`import { Heading } from '@/components/z-ui/typography/heading';
import { Label } from '@/components/z-ui/typography/label';
import { Paragraph } from '@/components/z-ui/typography/paragraph';

function MyComponent() {
  return (
    <div>
      <Heading size="large">Welcome to Z-UI</Heading>
      <Paragraph size="medium">
        Start building with our typography components.
      </Paragraph>
      <Label size="small">Built with React and Tailwind CSS</Label>
    </div>
  );
}`}
          </div>
        </div>
      </div>

      {/* Configuration */}
      <div className="space-y-6">
        <Heading size="medium">Configuration</Heading>
        
        <div className="space-y-4">
          <Label size="medium" className="font-semibold">TypeScript Support</Label>
          <Paragraph size="medium">
            Z-UI components are built with TypeScript and provide full type safety. Make sure your 
            tsconfig.json includes the component paths:
          </Paragraph>
          
          <div className="rounded bg-muted p-4 text-sm font-mono">
            {`// tsconfig.json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}`}
          </div>
        </div>

        <div className="space-y-4">
          <Label size="medium" className="font-semibold">Haptic Feedback (Optional)</Label>
          <Paragraph size="medium">
            If you want to use haptic feedback features, implement the haptic trigger function:
          </Paragraph>
          
          <div className="rounded bg-muted p-4 text-sm font-mono">
            {`// src/lib/haptic.ts
import { PlayHapticType } from '@/components/z-ui/types';

export function triggerHaptic(type: PlayHapticType) {
  // Implementation depends on your platform
  if ('vibrate' in navigator) {
    const patterns = {
      [PlayHapticType.LightTap]: [10],
      [PlayHapticType.MediumTap]: [20],
      [PlayHapticType.HeavyTap]: [50],
      [PlayHapticType.Success]: [10, 10, 10],
      [PlayHapticType.Error]: [50, 50, 50],
      // Add more patterns as needed
    };
    
    navigator.vibrate(patterns[type] || [20]);
  }
}`}
          </div>
        </div>
      </div>

      {/* Troubleshooting */}
      <div className="space-y-6">
        <Heading size="medium">Troubleshooting</Heading>
        
        <div className="space-y-4">
          <div className="rounded-lg border p-6">
            <Label size="medium" className="font-semibold mb-2 block">Common Issues</Label>
            <div className="space-y-3">
              <div>
                <Label size="small" className="font-semibold">Styles not applying</Label>
                <Paragraph size="small" className="text-muted-foreground">
                  Make sure Tailwind CSS is properly configured and the custom typography classes are added to your config.
                </Paragraph>
              </div>
              
              <div>
                <Label size="small" className="font-semibold">Import errors</Label>
                <Paragraph size="small" className="text-muted-foreground">
                  Check that the path aliases are correctly set up in your tsconfig.json and bundler configuration.
                </Paragraph>
              </div>
              
              <div>
                <Label size="small" className="font-semibold">TypeScript errors</Label>
                <Paragraph size="small" className="text-muted-foreground">
                  Ensure all required dependencies are installed and your TypeScript version is compatible.
                </Paragraph>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Next Steps */}
      <div className="space-y-6">
        <Heading size="medium">Next Steps</Heading>
        
        <div className="space-y-4">
          <Paragraph size="medium">
            Now that you have Z-UI installed, explore the component documentation to learn about 
            all available features and customization options.
          </Paragraph>
          
          <div className="grid gap-4 md:grid-cols-2">
            <div className="rounded-lg border p-4">
              <Label size="medium" className="font-semibold">Component Documentation</Label>
              <Paragraph size="small" className="text-muted-foreground mt-1">
                Learn about each component's props, variants, and usage examples.
              </Paragraph>
            </div>
            
            <div className="rounded-lg border p-4">
              <Label size="medium" className="font-semibold">Foundation</Label>
              <Paragraph size="small" className="text-muted-foreground mt-1">
                Understand the underlying types and design principles.
              </Paragraph>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InstallationPage;
