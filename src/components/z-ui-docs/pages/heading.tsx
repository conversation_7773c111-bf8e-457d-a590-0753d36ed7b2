import { Heading } from '../../z-ui/typography/heading';
import { Label } from '../../z-ui/typography/label';
import { Paragraph } from '../../z-ui/typography/paragraph';
import { PlayHapticType } from '../../z-ui/types';

const HeadingPage = () => {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <Heading size="large">Heading</Heading>
        <Paragraph size="medium" className="text-muted-foreground">
          A flexible heading component that renders semantic HTML heading elements with consistent styling.
        </Paragraph>
      </div>

      {/* Examples */}
      <div className="space-y-6">
        <Heading size="medium" id="examples">Examples</Heading>
        
        {/* Size Variants */}
        <div className="space-y-4">
          <Label size="medium" className="font-semibold">Size Variants</Label>
          <div className="rounded-lg border p-6 space-y-4">
            <div className="space-y-2">
              <Label size="small" className="text-muted-foreground">Large (h1)</Label>
              <Heading size="large">This is a large heading</Heading>
            </div>
            
            <div className="space-y-2">
              <Label size="small" className="text-muted-foreground">Medium (h2) - Default</Label>
              <Heading size="medium">This is a medium heading</Heading>
            </div>
            
            <div className="space-y-2">
              <Label size="small" className="text-muted-foreground">Small (h3)</Label>
              <Heading size="small">This is a small heading</Heading>
            </div>
          </div>
        </div>

        {/* AsChild Example */}
        <div className="space-y-4">
          <Label size="medium" className="font-semibold">AsChild Usage</Label>
          <div className="rounded-lg border p-6 space-y-4">
            <div className="space-y-2">
              <Label size="small" className="text-muted-foreground">Renders as span element</Label>
              <Heading size="medium" asChild>
                <span className="text-blue-600">Custom span heading</span>
              </Heading>
            </div>
          </div>
        </div>

        {/* Code Examples */}
        <div className="space-y-4">
          <Label size="medium" className="font-semibold">Code Examples</Label>
          <div className="space-y-4">
            <div>
              <Label size="small" className="text-muted-foreground mb-2 block">Basic Usage</Label>
              <div className="rounded bg-muted p-4 text-sm font-mono">
                {`<Heading size="large">Page Title</Heading>
<Heading size="medium">Section Title</Heading>
<Heading size="small">Subsection Title</Heading>`}
              </div>
            </div>
            
            <div>
              <Label size="small" className="text-muted-foreground mb-2 block">With Custom Styling</Label>
              <div className="rounded bg-muted p-4 text-sm font-mono">
                {`<Heading size="medium" className="text-blue-600 mb-4">
  Custom Styled Heading
</Heading>`}
              </div>
            </div>
            
            <div>
              <Label size="small" className="text-muted-foreground mb-2 block">AsChild Pattern</Label>
              <div className="rounded bg-muted p-4 text-sm font-mono">
                {`<Heading size="large" asChild>
  <h1 className="custom-class">Custom Element</h1>
</Heading>`}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* API Reference */}
      <div className="space-y-6">
        <Heading size="medium" id="api-reference">API Reference</Heading>
        
        <div className="space-y-4">
          <Label size="medium" className="font-semibold">Props</Label>
          <div className="rounded-lg border">
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-4 font-medium">Prop</th>
                    <th className="text-left p-4 font-medium">Type</th>
                    <th className="text-left p-4 font-medium">Default</th>
                    <th className="text-left p-4 font-medium">Description</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b">
                    <td className="p-4 font-mono">size</td>
                    <td className="p-4 font-mono">"large" | "medium" | "small"</td>
                    <td className="p-4 font-mono">"medium"</td>
                    <td className="p-4">Controls the heading size and semantic element</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-4 font-mono">asChild</td>
                    <td className="p-4 font-mono">boolean</td>
                    <td className="p-4 font-mono">false</td>
                    <td className="p-4">Renders as Slot component when true</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-4 font-mono">haptic</td>
                    <td className="p-4 font-mono">PlayHapticType</td>
                    <td className="p-4 font-mono">undefined</td>
                    <td className="p-4">Haptic feedback type for interactions</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-4 font-mono">children</td>
                    <td className="p-4 font-mono">React.ReactNode</td>
                    <td className="p-4 font-mono">-</td>
                    <td className="p-4">Content to be rendered inside the heading</td>
                  </tr>
                  <tr>
                    <td className="p-4 font-mono">className</td>
                    <td className="p-4 font-mono">string</td>
                    <td className="p-4 font-mono">undefined</td>
                    <td className="p-4">Additional CSS classes</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <Label size="medium" className="font-semibold">HTML Output</Label>
          <div className="rounded-lg border p-4">
            <div className="space-y-2 text-sm">
              <div><span className="font-mono">size="large"</span> → <span className="font-mono">&lt;h1&gt;</span></div>
              <div><span className="font-mono">size="medium"</span> → <span className="font-mono">&lt;h2&gt;</span></div>
              <div><span className="font-mono">size="small"</span> → <span className="font-mono">&lt;h3&gt;</span></div>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <Label size="medium" className="font-semibold">Haptic Feedback Types</Label>
          <div className="rounded-lg border p-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
              {Object.values(PlayHapticType).map((type) => (
                <div key={type} className="font-mono bg-muted px-2 py-1 rounded">
                  {type}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Usage Guidelines */}
      <div className="space-y-6">
        <Heading size="medium" id="usage-guidelines">Usage Guidelines</Heading>
        
        <div className="space-y-4">
          <div className="rounded-lg border p-6">
            <Label size="medium" className="font-semibold text-green-600 mb-2 block">✅ Do</Label>
            <ul className="space-y-1 text-sm">
              <li>• Use semantic heading hierarchy (h1 → h2 → h3)</li>
              <li>• Choose appropriate size based on content importance</li>
              <li>• Use asChild for custom styling while maintaining semantics</li>
              <li>• Apply haptic feedback for interactive headings</li>
            </ul>
          </div>
          
          <div className="rounded-lg border p-6">
            <Label size="medium" className="font-semibold text-red-600 mb-2 block">❌ Don't</Label>
            <ul className="space-y-1 text-sm">
              <li>• Skip heading levels (h1 → h3)</li>
              <li>• Use headings for styling purposes only</li>
              <li>• Override semantic meaning without good reason</li>
              <li>• Use multiple h1 elements on the same page</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeadingPage;
