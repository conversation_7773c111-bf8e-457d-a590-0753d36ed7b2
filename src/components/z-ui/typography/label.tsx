import { HapticFeedback } from "../types";
import { cva, VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const labelVariant = cva("text-dark-500", {
  variants: {
    size: {
      large: "text-label-large",
      medium: "text-label-medium",
      small: "text-label-small",
      xsmall:"text-label-xsmall",
    },
  },
  defaultVariants: {
    size: "medium",
  },
});

export interface LabelProps
  extends HapticFeedback,
    React.ComponentProps<"label">,
    VariantProps<typeof labelVariant> {
  size: "large" | "medium" | "small" | "xsmall";
  children: React.ReactNode;
}

export const Label = ({
  size = "medium",
  children,
  className,
}: LabelProps) => {
  return (
    // biome-ignore lint/a11y/noLabelWithoutControl: <explanation>
    <label className={cn(labelVariant({ size, className }))}>{children}</label>
  );
};
