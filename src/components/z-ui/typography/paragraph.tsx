import { cva, VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const paragraphVariant = cva("text-dark-500", {
  variants: {
    size: {
      medium: "text-paragraph-medium",
      small: "text-paragraph-small",
    },
  },
  defaultVariants: {
    size: "medium",
  },
});

export interface ParagraphProps
  extends React.ComponentProps<"p">,
    VariantProps<typeof paragraphVariant> {
  size: "medium" | "small";
  children: React.ReactNode;
}

export const Paragraph = ({
  size = "medium",
  children,
  className,
}: ParagraphProps) => {
  return (
    <p className={cn(paragraphVariant({ size, className }))}>{children}</p>
  );
};
