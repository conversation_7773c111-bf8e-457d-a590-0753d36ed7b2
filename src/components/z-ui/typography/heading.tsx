import { Slot } from "@radix-ui/react-slot";
import { HapticFeedback } from "../types";
import { cva, VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const headingVariant = cva("text-dark-500", {
  variants: {
    size: {
      large: "text-heading-large",
      medium: "text-heading-medium",
      small: "text-heading-small",
    },
  },
  defaultVariants: {
    size: "medium",
  },
});

export interface HeadingProps
  extends HapticFeedback,
    React.ComponentProps<"h1">,
    VariantProps<typeof headingVariant> {
  size: "large" | "medium" | "small";
  children: React.ReactNode;
  asChild?: boolean;
}

export const Heading = ({
  size = "medium",
  children,
  asChild,
  className
}: HeadingProps) => {
  const Comp = asChild
    ? Slot
    : {
        large: "h1",
        medium: "h2",
        small: "h3",
      }[size];

  return <Comp className={cn(headingVariant({ size, className }))}>{children}</Comp>;
};
