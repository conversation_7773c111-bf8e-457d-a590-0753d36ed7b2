# Z-UI Component Library

A comprehensive typography component library for the Buy Now Pay Later app.

## Components Overview

### 1. Heading Component (`heading.tsx`)
A flexible heading component that renders semantic HTML heading elements.

**Props:**
- `size`: "large" | "medium" | "small" (default: "medium")
- `asChild`: boolean (optional) - renders as Slot when true
- `haptic`: PlayHapticType (optional) - haptic feedback type
- `children`: React.ReactNode
- `className`: string (optional)

**HTML Output:**
- `large` → `<h1>`
- `medium` → `<h2>` 
- `small` → `<h3>`

**Usage:**
```tsx
<Heading size="large">Main Title</Heading>
<Heading size="medium">Section Title</Heading>
<Heading size="small">Subsection Title</Heading>

// As child component
<Heading size="medium" asChild>
  <span>Custom element</span>
</Heading>
```

### 2. Label Component (`label.tsx`)
A label component for form elements and general text labeling.

**Props:**
- `size`: "large" | "medium" | "small" | "xsmall" (default: "medium")
- `haptic`: PlayHapticType (optional) - haptic feedback type
- `children`: React.ReactNode
- `className`: string (optional)

**Usage:**
```tsx
<Label size="large">Important Label</Label>
<Label size="medium">Standard Label</Label>
<Label size="small">Secondary Label</Label>
<Label size="xsmall">Metadata Label</Label>
```

### 3. Paragraph Component (`paragraph.tsx`)
A paragraph component for body text content.

**Props:**
- `size`: "medium" | "small" (default: "medium")
- `children`: React.ReactNode
- `className`: string (optional)

**Usage:**
```tsx
<Paragraph size="medium">
  This is standard body text for main content.
</Paragraph>
<Paragraph size="small">
  This is smaller text for secondary content.
</Paragraph>
```

## Haptic Feedback Types

The library includes haptic feedback support through the `PlayHapticType` enum:

- `HeavyTap` - Strong haptic feedback
- `LightTap` - Light haptic feedback
- `Selection` - Selection feedback
- `MediumTap` - Medium haptic feedback
- `Custom` - Custom haptic pattern
- `Success` - Success feedback
- `Error` - Error feedback
- `Soft` - Soft haptic feedback

## Styling

All components use:
- **Base color**: `text-dark-500`
- **CSS classes**: Tailwind-based typography classes
- **Variants**: Managed by `class-variance-authority` (CVA)

### Typography Classes Used:
- `text-heading-large`, `text-heading-medium`, `text-heading-small`
- `text-label-large`, `text-label-medium`, `text-label-small`, `text-label-xsmall`
- `text-paragraph-medium`, `text-paragraph-small`

## Viewing the Showcase

To see all components and their variants in action:

1. **Navigate to the showcase**: Visit `/z-ui-screen` in your browser
2. **From home screen**: Click the "Z-UI Component Showcase" button
3. **Direct access**: Use the green button on the home screen

The showcase includes:
- All component variants with visual examples
- Property documentation
- Usage examples
- Haptic feedback type reference
- Real-world layout examples

## Development

### Adding New Components
1. Create component file in appropriate subdirectory
2. Follow the existing pattern with CVA variants
3. Include HapticFeedback interface if needed
4. Update the showcase component to include examples
5. Add to this README

### Styling Guidelines
- Use CVA for variant management
- Maintain consistent base styling (`text-dark-500`)
- Follow Tailwind typography scale
- Include proper TypeScript interfaces

## File Structure
```
src/components/z-ui/
├── types.ts              # Shared types and enums
├── typography/
│   ├── heading.tsx       # Heading component
│   ├── label.tsx         # Label component
│   └── paragraph.tsx     # Paragraph component
└── README.md            # This documentation
```

## Dependencies
- `@radix-ui/react-slot` - For asChild functionality
- `class-variance-authority` - For variant management
- `@/lib/utils` - Utility functions (cn helper)
