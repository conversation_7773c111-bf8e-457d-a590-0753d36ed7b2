
//base font-size should is 16px;

export const extendTheme = {
  transitionProperty: {
    width: "width",
  },
  fontSize: {
    'label-large': ["1rem", "1.25rem"], // 16px 20px,
    'label-medium': ["0.875rem", "1.125rem"], // 14px 18px
    'label-small': ["0.75rem", "1rem"], // 12px 16px
    'label-xsmall': ["0.625rem", "0.75rem"], // 10px 12px    
    "heading-large": ["1.5rem", "2.25rem"], // 24px 36px 
    "heading-medium": ["1rem", "1.5rem"], // 16px 24px
    "heading-small": ["1rem", "1.20rem"], // 18px 20px
    "paragraph-large": ["1rem, 1.5rem"], // 16px 24px
    "paragraph-medium": ["0.875rem", "1.375rem"], // 14px 22px
  },
  animation: {
    fillWidth: "fill-width 3s ease-in-out forwards",
    "marquee-left": "marquee-left var(--duration, 40s) linear infinite",
    "marquee-up": "marquee-up var(--duration, 40s) linear infinite",
    "slide-in": "slide-in 1s ease var(--slide-in-delay, 0) forwards",
    "force-in": "force-in var(--force-in-delay, 0.4s) cubic-bezier(0.22, 1, 0.36, 1) forwards",
    "image-show": "image-show 0.2s ease-in-out",
    "blur-out": "blurOut var(--duration, 0.15s) ease-in forwards"
  },
  keyframes: {
    "fill-width": {
      "0%": { width: 0 },
      "100%": { width: "100%" },
    },
    "marquee-left": {
      from: { transform: "translateX(0)" },
      to: { transform: "translateX(calc(-100% - var(--gap)))" },
    },
    "marquee-up": {
      from: { transform: "translateY(0)" },
      to: { transform: "translateY(calc(-100% - var(--gap)))" },
    },
    "slide-in": {
      from: {
        opacity: "0",
        transform: "translateY(-10px)",
      },
      to: {
        opacity: "1",
        transform: "translateY(0)",
      },
    },
    "force-in": {
      from: {
        opacity: 0.8,
        transform: "translateY(var(--force-in-translateY, 10px))",
        filter: "blur(var(--blurEnterFrom, 4px))",
      },
      to: {
        opacity: "1",
        transform: "translateY(0)",
        filter: "blur(--blurEnterTo, 0)",
      },
    },
    "image-show": {
      from: {
        filter: "blur(2px)",
      },
      to: {
        filter: "blur(0)",
      },
    },
    "blurOut": {
      from: {
        filter: "blur(4px)",
      },
      to: {
        filter: "blur(0)",
      }
    }
  },
  screens: {
    shortScreen: { raw: "(max-height: 667px)" },
  },
  padding: {
    "4.5": "1.125rem", //18px
  },
  margin: {
    "4.5": "1.125rem", //18px
  },
  borderWidth: {
    "0.5": "0.125rem",
  },
  borderRadius: {
    "extra-lg": "0.625rem", //10px
    xxl: "1.25rem", //20px
  },
  height: {
    "7.5": "1.875rem",
  },
};
