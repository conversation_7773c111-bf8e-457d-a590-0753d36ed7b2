import { Outlet, useLocation } from 'react-router-dom';
import { ZUILayout } from '../components/z-ui-docs/layout';

export const ZUIScreen = () => {
  const location = useLocation();

  // If we're at the root z-ui path, redirect to overview
  if (location.pathname.endsWith('/z-ui-screen') || location.pathname.endsWith('/z-ui-screen/')) {
    return <ZUILayout><Outlet /></ZUILayout>;
  }

  return (
    <ZUILayout>
      <Outlet />
    </ZUILayout>
  );
};

export default ZUIScreen;